using System;
using InertiaCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.Ekb.Web.Controllers;

[Authorize]
public class HomeController : AbpController
{
    public IActionResult Index()
    {
        return Inertia.Render("home", new
        {
            message = "Welcome to Imip.Ekb Dashboard",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous"
        });
    }

    [AllowAnonymous]
    public IActionResult Test()
    {
        return Inertia.Render("home", new
        {
            message = "Test Page - No Authentication Required",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = "Test User"
        });
    }
}