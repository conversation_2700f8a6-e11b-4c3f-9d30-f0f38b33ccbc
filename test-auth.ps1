# Test script to verify ROPC authentication flow
# This script tests the authentication against the external identity server

$authority = "https://identity.imip.co.id"
$clientId = "EkbLocal"
$clientSecret = "ivPBxTSyTvzHZu4DYop7SXgFdtVLyI1p"
$scope = "openid profile email"

# Test credentials (replace with actual test credentials)
$username = "admin"  # Common test username
$password = "1q2w3E*"  # Common test password

Write-Host "Testing ROPC Authentication Flow..." -ForegroundColor Green
Write-Host "Authority: $authority" -ForegroundColor Yellow
Write-Host "Client ID: $clientId" -ForegroundColor Yellow

# Prepare the request body
$body = @{
    grant_type = "password"
    username = $username
    password = $password
    client_id = $clientId
    client_secret = $clientSecret
    scope = $scope
}

try {
    Write-Host "Making ROPC request to token endpoint..." -ForegroundColor Blue
    
    $response = Invoke-RestMethod -Uri "$authority/connect/token" -Method Post -Body $body -ContentType "application/x-www-form-urlencoded"
    
    Write-Host "✅ Authentication successful!" -ForegroundColor Green
    Write-Host "Access Token: $($response.access_token.Substring(0, 50))..." -ForegroundColor Cyan
    Write-Host "Token Type: $($response.token_type)" -ForegroundColor Cyan
    Write-Host "Expires In: $($response.expires_in) seconds" -ForegroundColor Cyan
    
    if ($response.refresh_token) {
        Write-Host "Refresh Token: $($response.refresh_token.Substring(0, 50))..." -ForegroundColor Cyan
    }
    
    # Test the access token by calling userinfo endpoint
    Write-Host "`nTesting access token with userinfo endpoint..." -ForegroundColor Blue
    
    $headers = @{
        Authorization = "Bearer $($response.access_token)"
    }
    
    $userInfo = Invoke-RestMethod -Uri "$authority/connect/userinfo" -Method Get -Headers $headers
    
    Write-Host "✅ UserInfo retrieved successfully!" -ForegroundColor Green
    Write-Host "User Info: $($userInfo | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Authentication failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Response: $errorBody" -ForegroundColor Red

            # Try to parse as JSON for better formatting
            try {
                $errorJson = $errorBody | ConvertFrom-Json
                Write-Host "Parsed Error: $($errorJson | ConvertTo-Json -Depth 3)" -ForegroundColor Red
            } catch {
                Write-Host "Error response is not valid JSON" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
}

Write-Host "`nTest completed." -ForegroundColor Green
