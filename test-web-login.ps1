# Test script to simulate web application login
# This script tests the complete authentication workflow through the web application

$baseUrl = "http://localhost:5000"
$loginUrl = "$baseUrl/Account/Login"

# Test credentials that we know work
$username = "admin"
$password = "1q2w3E*"

Write-Host "Testing Web Application Login..." -ForegroundColor Green
Write-Host "Base URL: $baseUrl" -ForegroundColor Yellow

try {
    # Step 1: Get the login page to extract any required tokens (like anti-forgery tokens)
    Write-Host "`nStep 1: Getting login page..." -ForegroundColor Blue
    
    $session = New-Object Microsoft.PowerShell.Commands.WebRequestSession
    $loginPageResponse = Invoke-WebRequest -Uri $loginUrl -Method Get -SessionVariable session
    
    Write-Host "✅ Login page loaded successfully" -ForegroundColor Green
    Write-Host "Status Code: $($loginPageResponse.StatusCode)" -ForegroundColor Cyan
    
    # Extract anti-forgery token if present
    $antiForgeryToken = $null
    if ($loginPageResponse.Content -match 'name="__RequestVerificationToken"[^>]*value="([^"]*)"') {
        $antiForgeryToken = $matches[1]
        Write-Host "Anti-forgery token found: $($antiForgeryToken.Substring(0, 20))..." -ForegroundColor Cyan
    }
    
    # Step 2: Submit login form
    Write-Host "`nStep 2: Submitting login form..." -ForegroundColor Blue
    
    $loginData = @{
        'LoginInput.UserNameOrEmailAddress' = $username
        'LoginInput.Password' = $password
        'LoginInput.RememberMe' = 'false'
    }
    
    if ($antiForgeryToken) {
        $loginData['__RequestVerificationToken'] = $antiForgeryToken
    }
    
    $loginResponse = Invoke-WebRequest -Uri $loginUrl -Method Post -Body $loginData -WebSession $session -MaximumRedirection 0 -ErrorAction SilentlyContinue
    
    Write-Host "Login response status: $($loginResponse.StatusCode)" -ForegroundColor Cyan
    
    if ($loginResponse.StatusCode -eq 302) {
        $redirectLocation = $loginResponse.Headers.Location
        Write-Host "✅ Login successful! Redirecting to: $redirectLocation" -ForegroundColor Green
        
        # Step 3: Follow the redirect to see if we can access the protected home page
        Write-Host "`nStep 3: Following redirect to protected page..." -ForegroundColor Blue
        
        $homeResponse = Invoke-WebRequest -Uri $redirectLocation -Method Get -WebSession $session
        
        Write-Host "✅ Successfully accessed protected page!" -ForegroundColor Green
        Write-Host "Status Code: $($homeResponse.StatusCode)" -ForegroundColor Cyan
        Write-Host "Page Title: $($homeResponse.ParsedHtml.title)" -ForegroundColor Cyan
        
        # Check if the page contains expected content
        if ($homeResponse.Content -match "Welcome to Imip.Ekb Dashboard" -or $homeResponse.Content -match "admin") {
            Write-Host "✅ Page contains expected authenticated content!" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Page loaded but may not contain expected content" -ForegroundColor Yellow
        }
        
    } elseif ($loginResponse.StatusCode -eq 200) {
        Write-Host "❌ Login failed - returned to login page" -ForegroundColor Red
        
        # Check for error messages
        if ($loginResponse.Content -match 'class="[^"]*alert[^"]*"[^>]*>([^<]+)') {
            Write-Host "Error message: $($matches[1])" -ForegroundColor Red
        }
        
        if ($loginResponse.Content -match 'validation-summary-errors') {
            Write-Host "Validation errors found in response" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Unexpected response status: $($loginResponse.StatusCode)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error during login test!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
    }
}

Write-Host "`nTest completed." -ForegroundColor Green
