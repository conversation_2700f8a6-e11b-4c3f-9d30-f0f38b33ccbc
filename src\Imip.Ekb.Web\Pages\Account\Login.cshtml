@page
@model Imip.Ekb.Web.Pages.Account.IdentityCustomLoginModel
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.Account.Localization
@using Volo.Abp.Account.Settings
@using Volo.Abp.Settings
@inject IHtmlLocalizer<AccountResource> L
@inject ISettingProvider SettingProvider
@{
    Layout = "/Pages/Shared/CustomLayout.cshtml";
    ViewData["Title"] = "Login - IMIP Identity Server";
}

@section styles {
    <script src="/js/tailwind.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    backgroundImage: {
                        "hero-pattern":
                            "linear-gradient(to right bottom, rgba('#7ed56f',0.8), rgba('#28b485',0.8)), url('/img/img1.jpg')",
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                },
            },
        };
    </script>
}



<div class="min-h-screen flex bg-gradient-to-br from-slate-50 to-slate-100">
    <!-- Left side - Background with logo and illustration -->
    <div class="hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-emerald-600 via-emerald-500 to-teal-600 overflow-hidden">
        <!-- Decorative background elements -->
        <div class="absolute inset-0 bg-[url('/img/bg-image.png')] bg-cover bg-center opacity-20"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-600/90 via-emerald-500/80 to-teal-600/90"></div>

        <!-- Floating geometric shapes for visual interest -->
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-32 right-16 w-24 h-24 bg-white/5 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div class="absolute top-1/2 left-10 w-16 h-16 bg-white/15 rounded-full blur-md animate-pulse delay-500"></div>

        <!-- Header with logo and title -->
        <div class="absolute top-0 left-0 right-0 p-8 flex items-center justify-between z-20 animate-fade-in">
            <div class="flex items-center space-x-3">
                <img src="/img/logo-imip-white.png" alt="PT IMIP logo" class="h-12 w-auto drop-shadow-lg" />
                <div class="h-8 w-px bg-white/30"></div>
                <h1 class="text-white text-xl font-bold tracking-wide drop-shadow-md">Single Sign-On</h1>
            </div>
        </div>

        <div class="flex flex-col items-center justify-center h-full relative z-10 p-12 animate-slide-up">
            <div class="text-center mb-12">
                <img src="/img/3D-SSO-AUTH.png" alt="Authentication Illustration"
                     class="w-72 max-w-full h-auto mx-auto mb-8 drop-shadow-2xl hover:scale-105 transition-transform duration-300" />
            </div>

            <div class="max-w-md text-center space-y-4">
                <blockquote class="text-white text-lg font-medium leading-relaxed drop-shadow-md">
                    "Simplify your day — one login to access everything you need."
                </blockquote>
                <p class="text-emerald-100 font-bold text-xl tracking-wide drop-shadow-sm">#TogetherWeCan</p>
            </div>
        </div>
    </div>

    <!-- Right side - Login Form -->
    <div class="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 lg:p-16 bg-white relative overflow-hidden">
        <!-- Subtle background decoration -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-emerald-50 to-transparent rounded-full -translate-y-32 translate-x-32 opacity-50"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-teal-50 to-transparent rounded-full translate-y-24 -translate-x-24 opacity-30"></div>

        <div class="w-full max-w-md relative z-10 animate-slide-up">
            <!-- Mobile header (visible only on small screens) -->
            <div class="flex lg:hidden items-center justify-center mb-10 p-6 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl shadow-lg">
                <div class="flex items-center space-x-3">
                    <img src="/img/logo-imip-white.png" alt="PT IMIP logo" class="h-10 w-auto" />
                    <div class="h-6 w-px bg-white/30"></div>
                    <h2 class="text-lg font-bold text-white tracking-wide">Single Sign-On</h2>
                </div>
            </div>

            <!-- Welcome section -->
            <div class="text-center lg:text-left mb-10">
                <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-3 tracking-tight">Welcome Back</h1>
                <p class="text-gray-600 text-lg leading-relaxed">Please sign in to your account to continue</p>
            </div>

            @* Add Alert Messages *@
            @if (Model.Alerts.Any())
            {
                @foreach (var alert in Model.Alerts)
                {
                    <div class="mb-6 p-4 rounded-xl border-l-4 shadow-sm @(alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Danger ? "bg-red-50 text-red-800 border-red-400" :
                                                                                                       alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Warning ? "bg-yellow-50 text-yellow-800 border-yellow-400" :
                                                                                                       alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Success ? "bg-green-50 text-green-800 border-green-400" :
                                                                                                       "bg-blue-50 text-blue-800 border-blue-400")">
                        <div class="flex items-center">
                            @if (alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Danger)
                            {
                                <svg class="w-5 h-5 mr-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            }
                            else if (alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Success)
                            {
                                <svg class="w-5 h-5 mr-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            }
                            else if (alert.Type == Volo.Abp.AspNetCore.Mvc.UI.Alerts.AlertType.Warning)
                            {
                                <svg class="w-5 h-5 mr-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            }
                            else
                            {
                                <svg class="w-5 h-5 mr-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            }
                            <span class="font-medium">@alert.Text</span>
                        </div>
                    </div>
                }
            }

            <form method="post" class="space-y-6" id="loginForm" onsubmit="return handleLogin(event)">
                <input type="hidden" name="action" value="login" />
                <input type="hidden" name="ReturnUrl" value="@Model.ReturnUrl" />
                <input type="hidden" name="ReturnUrlHash" value="@Model.ReturnUrlHash" />

                <div class="space-y-2">
                    <label for="LoginInput_UserNameOrEmailAddress"
                        class="block text-sm font-semibold text-gray-800 mb-2">Username or Email</label>
                    <div class="relative group">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none transition-colors duration-200 group-focus-within:text-emerald-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-focus-within:text-emerald-500" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <input
                            class="w-full pl-12 pr-4 py-3.5 text-gray-900 placeholder-gray-500 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent focus:bg-white transition-all duration-200 hover:border-gray-300"
                            type="text" asp-for="LoginInput.UserNameOrEmailAddress"
                            placeholder="Enter your username or email" autofocus />
                    </div>
                    <span asp-validation-for="LoginInput.UserNameOrEmailAddress" class="text-red-500 text-sm font-medium flex items-center mt-1">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </span>
                </div>

                <div class="space-y-2">
                    <label for="LoginInput_Password"
                        class="block text-sm font-semibold text-gray-800 mb-2">Password</label>
                    <div class="relative group">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none transition-colors duration-200 group-focus-within:text-emerald-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-focus-within:text-emerald-500" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                        </div>
                        <input
                            class="w-full pl-12 pr-16 py-3.5 text-gray-900 placeholder-gray-500 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent focus:bg-white transition-all duration-200 hover:border-gray-300 js-password"
                            asp-for="LoginInput.Password" type="password" placeholder="Enter your password" />
                        <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <input class="hidden js-password-toggle" id="toggle" type="checkbox" />
                            <label
                                class="bg-gray-100 hover:bg-gray-200 rounded-lg px-3 py-1.5 text-xs font-medium text-gray-600 cursor-pointer transition-colors duration-200 js-password-label border border-gray-200 hover:border-gray-300"
                                for="toggle">show</label>
                        </div>
                    </div>
                    <span asp-validation-for="LoginInput.Password" class="text-red-500 text-sm font-medium flex items-center mt-1">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </span>
                </div>

                <div class="flex items-center justify-between py-2">
                    <div class="flex items-center">
                        <input asp-for="LoginInput.RememberMe"
                            class="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500 focus:ring-offset-0 transition-colors duration-200" />
                        <label asp-for="LoginInput.RememberMe" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer">Remember me</label>
                    </div>
                    <a href="#" class="text-sm font-medium text-emerald-600 hover:text-emerald-500 transition-colors duration-200">
                        Forgot password?
                    </a>
                </div>

                <div class="pt-4">
                    <button type="submit" id="loginButton"
                        class="w-full bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-700 hover:to-emerald-600 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                        <span id="loginText" class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            Sign In
                        </span>
                        <svg id="loadingSpinner" class="hidden animate-spin h-5 w-5 mx-auto"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                    </button>
                </div>
            </form>

            <!-- Divider -->
            <div class="relative my-8">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-200"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-4 bg-white text-gray-500 font-medium">Need help?</span>
                </div>
            </div>

            <!-- Support and Footer -->
            <div class="text-center space-y-4">
                <div class="bg-gray-50 rounded-xl p-4 border border-gray-100">
                    <p class="text-sm text-gray-700 font-medium mb-2">Having trouble signing in?</p>
                    <p class="text-sm text-gray-600">
                        Contact our IT Application Development Team:
                        <a href="tel:737" class="font-semibold text-emerald-600 hover:text-emerald-500 transition-colors duration-200">737</a>
                    </p>
                </div>

                <div class="pt-4 border-t border-gray-100">
                    <p class="text-xs text-gray-500 leading-relaxed">
                        &copy; <span id="year"></span> Information Technology<br>
                        <span class="font-medium">PT Indonesia Morowali Industrial Park</span><br>
                        All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>


@section scripts {
    <script>
        document.getElementById('year').textContent = new Date().getFullYear();

        // Password toggle functionality
        const passwordToggle = document.querySelector('.js-password-toggle');
        const password = document.querySelector('.js-password');
        const passwordLabel = document.querySelector('.js-password-label');

        passwordToggle.addEventListener('change', function () {
            if (password.type === 'password') {
                password.type = 'text';
                passwordLabel.textContent = 'hide';
            } else {
                password.type = 'password';
                passwordLabel.textContent = 'show';
            }
        });

        // Login form submission handler
        function handleLogin(event) {
            const form = document.getElementById('loginForm');
            const button = document.getElementById('loginButton');
            const spinner = document.getElementById('loadingSpinner');
            const loginText = document.getElementById('loginText');

            // Basic form validation
            if (!form.checkValidity()) {
                return true; // Let the browser handle invalid form
            }

            button.disabled = true;
            spinner.classList.remove('hidden');
            loginText.classList.add('hidden');

            return true; // Allow form submission to proceed
        }

        // Active Directory login form submission handler
        function handleAdLogin(event) {
            const form = document.getElementById('adLoginForm');
            const button = document.getElementById('adLoginButton');
            const spinner = document.getElementById('adLoadingSpinner');
            const loginText = document.getElementById('adLoginText');

            // Basic form validation
            if (!form.checkValidity()) {
                return true; // Let the browser handle invalid form
            }

            // Show loading state
            button.disabled = true;
            spinner.classList.remove('hidden');
            loginText.classList.add('mr-2');

            // Try to detect Windows authentication
            // This is a simplified approach - in a real implementation,
            // you might use more sophisticated techniques

            // For demonstration, we'll set a cookie that the server can read
            // In a real implementation, you might use SPNEGO/Kerberos or other methods

            // Try to get the current domain username if available
            let windowsUser = '';

            // Try to get from browser environment
            // Note: This is not reliable and will only work in specific environments
            if (typeof window.external !== 'undefined' &&
                typeof window.external.GetUserInfo === 'function') {
                try {
                    windowsUser = window.external.GetUserInfo();
                } catch (e) {
                    console.log('Could not get Windows user info');
                }
            }

            // Set a cookie with the Windows username (if available)
            if (windowsUser) {
                document.cookie = `WindowsAuthUser=${windowsUser}; path=/; secure; samesite=strict`;
            }

            // Continue with form submission
            return true; // Allow form submission to proceed
        }
    </script>
}