using Imip.Ekb.EntityFrameworkCore;
using Imip.Ekb.Localization;
using Imip.Ekb.MultiTenancy;
using Imip.Ekb.Web.HealthChecks;
using Imip.Ekb.Web.Menus;
using Imip.Ekb.Web.Middleware;
using Imip.Ekb.Web.Services;
using Imip.Ekb.Web.Services.Interfaces;
using InertiaCore.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.IO;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity.Web;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.BackgroundJobs.Quartz;
using Volo.Abp.BackgroundWorkers.Quartz;
using Volo.Abp.Quartz;
using Quartz;
using Imip.Ekb.Web.Extensions;
using Imip.Ekb.SilkierQuartz;
using Quartz.Util;

namespace Imip.Ekb.Web;

[DependsOn(
    typeof(EkbHttpApiModule),
    typeof(EkbApplicationModule),
    typeof(EkbEntityFrameworkCoreModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpCachingStackExchangeRedisModule),
    typeof(AbpBackgroundJobsQuartzModule),
    typeof(AbpBackgroundWorkersQuartzModule)
)]
public class EkbWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(EkbResource),
                typeof(EkbDomainModule).Assembly,
                typeof(EkbDomainSharedModule).Assembly,
                typeof(EkbApplicationModule).Assembly,
                typeof(EkbApplicationContractsModule).Assembly,
                typeof(EkbWebModule).Assembly
            );
        });

        PreConfigure<AbpQuartzOptions>(options =>
        {
            options.Configurator = configure =>
            {
                configure.SetProperty("quartz.plugin.recentHistory.type", typeof(AbpExecutionHistoryPlugin).AssemblyQualifiedNameWithoutVersion());
                configure.SetProperty("quartz.plugin.recentHistory.storeType", typeof(AbpExecutionHistoryStore).AssemblyQualifiedNameWithoutVersion());
                configure.UsePersistentStore(storeOptions =>
                {
                    storeOptions.UseProperties = true;
                    storeOptions.UseNewtonsoftJsonSerializer();
                    storeOptions.UseSqlServer(configuration.GetConnectionString("Default")!);
                    storeOptions.UseClustering(c =>
                    {
                        c.CheckinMisfireThreshold = TimeSpan.FromSeconds(20);
                        c.CheckinInterval = TimeSpan.FromSeconds(10);
                    });
                });

                // Configure schema for Quartz tables
                configure.SetProperty("quartz.jobStore.tablePrefix", "Quartz.QRTZ_");
                configure.SetProperty("quartz.jobStore.driverDelegateType", "Quartz.Impl.AdoJobStore.SqlServerDelegate, Quartz");

                // Disable schema validation to prevent startup failures if tables don't exist yet
                // This provides a safety net in case the migration hasn't completed
                configure.SetProperty("quartz.jobStore.performSchemaValidation", "false");
            };
        });
    }


    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
        var appName = configuration["App:AppName"] ?? "Imip.Ekb";

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }

        DataProtectionConfigurationService.ConfigureDataProtection(context.Services, configuration, hostingEnvironment);
        DistributedCacheConfigurationService.ConfigureDistributedCache(context.Services, configuration);

        if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
        {
            // Configure<OpenIddictServerAspNetCoreOptions>(options =>
            // {
            //     options.DisableTransportSecurityRequirement = true;
            // });

            Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedHost;
                options.RequireHeaderSymmetry = false;
                options.ForwardedProtoHeaderName = "X-Forwarded-Proto";
                options.ForwardedHostHeaderName = "X-Forwarded-Host";

                // Trust the nginx proxy
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
                options.KnownProxies.Add(System.Net.IPAddress.Any);
            });
        }

        AntiforgeryConfigurationService.ConfigureAntiforgery(context.Services, configuration, hostingEnvironment);


        context.Services.AddInertia(options =>
        {
            options.RootView = "~/Views/App.cshtml";
        });

        context.Services.AddViteHelper(options =>
        {
            options.PublicDirectory = "wwwroot";
            options.ManifestFilename = "manifest.json";
            options.BuildDirectory = "build";
        });

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureHealthChecks(context);

        // Register services before authentication configuration
        // Register the HttpContextAccessor if it's not already registered
        context.Services.AddHttpContextAccessor();

        // Register HttpClientFactory for testing and other HTTP operations
        context.Services.AddHttpClient();

        // Register token service
        context.Services.AddScoped<ITokenService, TokenService>();
        context.Services.AddScoped<IAuthenticationTokenValidationService, AuthenticationTokenValidationService>();
        context.Services.AddScoped<ExternalApiService>();
        context.Services.AddScoped<AppToAppService>();

        context.Services.AddScoped<IUserSynchronizationService, UserSynchronizationService>();


        // Register background token refresh service
        // context.Services.AddHostedService<BackgroundTokenRefreshService>();

        AuthenticationConfigurationService.ConfigureAuthentication(context.Services, configuration);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        SwaggerConfigurationService.ConfigureSwagger(context.Services);

        // Configure authorization policies
        ConfigureAuthorizationPolicies(context.Services);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
        });

        // Configure session for deferred user synchronization
        context.Services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
            options.Cookie.Name = ".Imip.Ekb.Session";
        });
    }


    private void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddEkbHealthChecks();
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                }
            );

            options.ScriptBundles.Configure(
                LeptonXLiteThemeBundles.Scripts.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<EkbWebModule>();
        });
    }

    private static bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<EkbWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.Ekb.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<EkbDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.Ekb.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<EkbDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.Ekb.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<EkbApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.Ekb.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<EkbApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.Ekb.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<EkbHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<EkbWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new EkbMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new EkbToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(EkbApplicationModule).Assembly, opts =>
            {
                opts.RootPath = "ekb";
            });
        });
    }

    private void ConfigureAuthorizationPolicies(IServiceCollection services)
    {
        services.AddAuthorization(options =>
        {
            // Add a specific policy for Quartz endpoints
            options.AddPolicy("QuartzAccess", policy =>
            {
                policy.RequireAuthenticatedUser();
                // You can add additional requirements here if needed
                // For example, require specific roles or permissions
                // policy.RequireRole("Admin");
                // policy.RequirePermission("SilkierQuartz");
            });
        });
    }


    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        app.UseForwardedHeaders();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseAbpSilkierQuartz();

        // Add pod info to response headers
        app.UsePodInfo();

        app.UseInertia();
        //app.UseCsrfTokenMiddleware();

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseAuthentication();

        // Add token refresh middleware
        // app.UseMiddleware<TokenRefreshMiddleware>();

        // app.UseAbpOpenIddictValidation();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "Ekb API");
        });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapAbpSilkierQuartz();
            endpoints.MapControllerRoute(
                name: "default",
                pattern: "{controller=Home}/{action=Index}/{id?}");
        });
    }
}
